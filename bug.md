I/InputMethodManager(21209): Starting input: Bind resultString=SUCCESS_WAITING_IME_SESSION
I/flutter (21209): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): │ #0   FileImportService._checkInitialImportFile (package:keepdance/services/file_import_service.dart:66:15)
I/flutter (21209): │ #1   <asynchronous suspension>
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ 🐛 文件导入功能在当前平台暂不可用，应用将正常运行
I/flutter (21209): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): │ MissingPluginException(No implementation found for method initialize on channel keepdance_pose)
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ #0   MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:368:7)
I/flutter (21209): │ #1   <asynchronous suspension>
I/flutter (21209): │ #2   PosePlugin.initialize (package:keepdance/pose/utils/pose_plugin.dart:65:7)
I/flutter (21209): │ #3   <asynchronous suspension>
I/flutter (21209): │ #4   PosePluginManager._performInitialization (package:keepdance/pose/utils/pose_plugin_manager.dart:137:9)
I/flutter (21209): │ #5   <asynchronous suspension>
I/flutter (21209): │ #6   PosePluginManager.initialize (package:keepdance/pose/utils/pose_plugin_manager.dart:115:7)
I/flutter (21209): │ #7   <asynchronous suspension>
I/flutter (21209): │ #8   CustomApp.main (package:keepdance/main.dart:93:15)
I/flutter (21209): │ #9   <asynchronous suspension>
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ 16:20:58.210 (+0:00:00.078214)
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ ⛔ 姿势检测插件: 检测器初始化失败: MissingPluginException(No implementation found for method initialize on channel keepdance_pose)
I/flutter (21209): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): │ #0   PosePluginManager._performInitialization (package:keepdance/pose/utils/pose_plugin_manager.dart:165:15)
I/flutter (21209): │ #1   <asynchronous suspension>
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ 🐛 姿势检测插件在当前平台暂不可用，跳过初始化
I/flutter (21209): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21209): │ #0   CustomApp.main (package:keepdance/main.dart:255:14)
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ 16:20:58.213 (+0:00:00.080499)
I/flutter (21209): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21209): │ ⛔ 应用初始化失败: Null check operator used on a null value
I/flutter (21209): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Gralloc4(21209): mapper 4.x is not supported
W/Gralloc3(21209): mapper 3.x is not supported
W/Gralloc4(21209): allocator 4.x is not supported
W/Gralloc3(21209): allocator 3.x is not supported
I/Choreographer(21209): Skipped 35 frames! The application may be doing too much work on its main thread.
I/InputMethodManager(21209): Starting input: reason=BOUND_TO_IMMS